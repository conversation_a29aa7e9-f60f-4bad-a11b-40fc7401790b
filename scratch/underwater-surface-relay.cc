/*
 * 水下-水上混合网络仿真
 * 包含水下节点(UAN)、浮标中继节点(Buoy)、船舶节点(Ship)和无人机节点(UAV)
 * 浮标节点实现UAN和WiFi之间的协议转换和数据中继
 */

#include "ns3/applications-module.h"
#include "ns3/bridge-module.h"
#include "ns3/core-module.h"
#include "ns3/internet-module.h"
#include "ns3/mobility-module.h"
#include "ns3/network-module.h"
#include "ns3/uan-module.h"
#include "ns3/wifi-module.h"

#include <fstream>
#include <iostream>

using namespace ns3;

NS_LOG_COMPONENT_DEFINE("UnderwaterSurfaceRelay");

// 全局变量用于统计
uint32_t g_uanTxCount = 0;
uint32_t g_uanRxCount = 0;
uint32_t g_wifiTxCount = 0;
uint32_t g_wifiRxCount = 0;
uint32_t g_buoyUanRxCount = 0;
uint32_t g_buoyWifiRxCount = 0;
uint32_t g_buoyUanTxCount = 0;
uint32_t g_buoyWifiTxCount = 0;

// 数据包跟踪回调函数
void UanPhyTxTrace(std::string context, Ptr<const Packet> packet, double txPowerDb, UanTxMode mode)
{
    g_uanTxCount++;
    NS_LOG_INFO("UAN TX: " << context << " Packet UID: " << packet->GetUid()
                << " Size: " << packet->GetSize() << " bytes at " << Simulator::Now().GetSeconds() << "s");
}

void UanPhyRxTrace(std::string context, Ptr<const Packet> packet, double snr, UanTxMode mode)
{
    g_uanRxCount++;
    NS_LOG_INFO("UAN RX: " << context << " Packet UID: " << packet->GetUid()
                << " Size: " << packet->GetSize() << " bytes SNR: " << snr << " at " << Simulator::Now().GetSeconds() << "s");
}

void UanPhyTxBeginTrace(std::string context, Ptr<const Packet> packet)
{
    g_uanTxCount++;
    NS_LOG_INFO("UAN TX Begin: " << context << " Packet UID: " << packet->GetUid()
                << " Size: " << packet->GetSize() << " bytes at " << Simulator::Now().GetSeconds() << "s");
}

void WifiPhyTxTrace(std::string context, Ptr<const Packet> packet, double txPowerW)
{
    g_wifiTxCount++;
    NS_LOG_INFO("WiFi TX: " << context << " Packet UID: " << packet->GetUid() 
                << " Size: " << packet->GetSize() << " bytes at " << Simulator::Now().GetSeconds() << "s");
}

void WifiPhyRxTrace(std::string context, Ptr<const Packet> packet, RxPowerWattPerChannelBand rxPowersW)
{
    g_wifiRxCount++;
    NS_LOG_INFO("WiFi RX: " << context << " Packet UID: " << packet->GetUid() 
                << " Size: " << packet->GetSize() << " bytes at " << Simulator::Now().GetSeconds() << "s");
}

// 应用层数据包接收回调
void PacketReceived(std::string context, Ptr<const Packet> packet)
{
    NS_LOG_INFO("App RX: " << context << " Packet UID: " << packet->GetUid()
                << " Size: " << packet->GetSize() << " bytes"
                << " at " << Simulator::Now().GetSeconds() << "s");
}

// 简化的中继应用类 - 使用UDP套接字实现数据转发
class RelayApplication : public Application
{
public:
    static TypeId GetTypeId();
    RelayApplication();
    virtual ~RelayApplication();

    void SetUanAddress(Ipv4Address uanAddr);
    void SetWifiAddress(Ipv4Address wifiAddr);

private:
    virtual void StartApplication();
    virtual void StopApplication();

    void HandleUanRead(Ptr<Socket> socket);
    void HandleWifiRead(Ptr<Socket> socket);
    void SendTestPacket();

    Ptr<Socket> m_uanSocket;
    Ptr<Socket> m_wifiSocket;
    Ipv4Address m_uanAddress;
    Ipv4Address m_wifiAddress;
    uint16_t m_port;
    EventId m_testEvent;
};

TypeId RelayApplication::GetTypeId()
{
    static TypeId tid = TypeId("RelayApplication")
                            .SetParent<Application>()
                            .SetGroupName("Applications")
                            .AddConstructor<RelayApplication>();
    return tid;
}

RelayApplication::RelayApplication()
    : m_port(9999)
{
}

RelayApplication::~RelayApplication()
{
}

void RelayApplication::SetUanAddress(Ipv4Address uanAddr)
{
    m_uanAddress = uanAddr;
}

void RelayApplication::SetWifiAddress(Ipv4Address wifiAddr)
{
    m_wifiAddress = wifiAddr;
}

void RelayApplication::StartApplication()
{
    NS_LOG_FUNCTION(this);

    // 创建UDP套接字监听UAN网络
    m_uanSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
    InetSocketAddress uanLocal = InetSocketAddress(m_uanAddress, m_port);
    int result1 = m_uanSocket->Bind(uanLocal);
    m_uanSocket->SetRecvCallback(MakeCallback(&RelayApplication::HandleUanRead, this));

    // 创建UDP套接字监听WiFi网络
    m_wifiSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
    InetSocketAddress wifiLocal = InetSocketAddress(m_wifiAddress, m_port);
    int result2 = m_wifiSocket->Bind(wifiLocal);
    m_wifiSocket->SetRecvCallback(MakeCallback(&RelayApplication::HandleWifiRead, this));

    NS_LOG_INFO("Relay application started on UAN: " << m_uanAddress << " WiFi: " << m_wifiAddress
                << " UAN bind result: " << result1 << " WiFi bind result: " << result2);

    // 安排测试数据包发送
    m_testEvent = Simulator::Schedule(Seconds(10.0), &RelayApplication::SendTestPacket, this);
}

void RelayApplication::SendTestPacket()
{
    NS_LOG_INFO("Sending test packet from relay at " << Simulator::Now().GetSeconds() << "s");

    // 创建测试数据包
    Ptr<Packet> testPacket = Create<Packet>((uint8_t*)"Test from relay", 15);

    // 发送到UAN网络
    Ptr<Socket> uanSendSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
    InetSocketAddress uanDest = InetSocketAddress(Ipv4Address("**********"), m_port);
    uanSendSocket->Connect(uanDest);
    uanSendSocket->Send(testPacket->Copy());
    uanSendSocket->Close();

    // 发送到WiFi网络
    Ptr<Socket> wifiSendSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
    InetSocketAddress wifiDest = InetSocketAddress(Ipv4Address("*************"), m_port);
    wifiSendSocket->Connect(wifiDest);
    wifiSendSocket->Send(testPacket->Copy());
    wifiSendSocket->Close();

    NS_LOG_INFO("Test packets sent");
}

void RelayApplication::StopApplication()
{
    NS_LOG_FUNCTION(this);
    if (m_uanSocket)
    {
        m_uanSocket->Close();
        m_uanSocket = nullptr;
    }
    if (m_wifiSocket)
    {
        m_wifiSocket->Close();
        m_wifiSocket = nullptr;
    }
}

void RelayApplication::HandleUanRead(Ptr<Socket> socket)
{
    NS_LOG_INFO("HandleUanRead called at " << Simulator::Now().GetSeconds() << "s");
    Ptr<Packet> packet;
    Address from;
    while ((packet = socket->RecvFrom(from)))
    {
        g_buoyUanRxCount++;
        NS_LOG_INFO("Buoy received from UAN: Packet UID " << packet->GetUid()
                    << " Size: " << packet->GetSize() << " bytes from " << from
                    << " at " << Simulator::Now().GetSeconds() << "s");

        // 转发到WiFi网络 - 广播
        Ptr<Socket> wifiSendSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
        InetSocketAddress wifiDest = InetSocketAddress(Ipv4Address("*************"), m_port);
        wifiSendSocket->Connect(wifiDest);
        wifiSendSocket->Send(packet->Copy());
        wifiSendSocket->Close();

        g_buoyWifiTxCount++;
        NS_LOG_INFO("Buoy forwarded to WiFi: Packet UID " << packet->GetUid()
                    << " at " << Simulator::Now().GetSeconds() << "s");
    }
}

void RelayApplication::HandleWifiRead(Ptr<Socket> socket)
{
    NS_LOG_INFO("HandleWifiRead called at " << Simulator::Now().GetSeconds() << "s");
    Ptr<Packet> packet;
    Address from;
    while ((packet = socket->RecvFrom(from)))
    {
        g_buoyWifiRxCount++;
        NS_LOG_INFO("Buoy received from WiFi: Packet UID " << packet->GetUid()
                    << " Size: " << packet->GetSize() << " bytes from " << from
                    << " at " << Simulator::Now().GetSeconds() << "s");

        // 转发到UAN网络 - 广播
        Ptr<Socket> uanSendSocket = Socket::CreateSocket(GetNode(), UdpSocketFactory::GetTypeId());
        InetSocketAddress uanDest = InetSocketAddress(Ipv4Address("**********"), m_port);
        uanSendSocket->Connect(uanDest);
        uanSendSocket->Send(packet->Copy());
        uanSendSocket->Close();

        g_buoyUanTxCount++;
        NS_LOG_INFO("Buoy forwarded to UAN: Packet UID " << packet->GetUid()
                    << " at " << Simulator::Now().GetSeconds() << "s");
    }
}

int main(int argc, char* argv[])
{
    // 启用日志
    LogComponentEnable("UnderwaterSurfaceRelay", LOG_LEVEL_INFO);
    LogComponentEnable("UdpEchoClientApplication", LOG_LEVEL_INFO);
    LogComponentEnable("UdpEchoServerApplication", LOG_LEVEL_INFO);

    // 解析命令行参数
    CommandLine cmd(__FILE__);
    cmd.Parse(argc, argv);

    // 创建节点
    NodeContainer uanNodes;      // 水下节点
    NodeContainer buoyNode;      // 浮标节点
    NodeContainer surfaceNodes;  // 水面节点(船舶和无人机)
    
    uanNodes.Create(2);          // 2个水下节点
    buoyNode.Create(1);          // 1个浮标节点
    surfaceNodes.Create(2);      // 2个水面节点(船舶和无人机)

    NS_LOG_INFO("Created " << uanNodes.GetN() << " UAN nodes, "
                << buoyNode.GetN() << " buoy node, "
                << surfaceNodes.GetN() << " surface nodes");

    // 设置移动性模型
    MobilityHelper mobility;

    // 水下节点位置 (深度50米)
    Ptr<ListPositionAllocator> uanPositionAlloc = CreateObject<ListPositionAllocator>();
    uanPositionAlloc->Add(Vector(0.0, 0.0, -50.0));    // UAN节点1
    uanPositionAlloc->Add(Vector(100.0, 0.0, -50.0));  // UAN节点2
    mobility.SetPositionAllocator(uanPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(uanNodes);

    // 浮标节点位置 (水面)
    Ptr<ListPositionAllocator> buoyPositionAlloc = CreateObject<ListPositionAllocator>();
    buoyPositionAlloc->Add(Vector(50.0, 0.0, 0.0));    // 浮标在中间位置
    mobility.SetPositionAllocator(buoyPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(buoyNode);

    // 水面节点位置 (高度10米)
    Ptr<ListPositionAllocator> surfacePositionAlloc = CreateObject<ListPositionAllocator>();
    surfacePositionAlloc->Add(Vector(30.0, 30.0, 10.0));   // 船舶
    surfacePositionAlloc->Add(Vector(70.0, 30.0, 50.0));   // 无人机(更高)
    mobility.SetPositionAllocator(surfacePositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(surfaceNodes);

    // 创建UAN信道和设备
    UanHelper uan;
    Ptr<UanChannel> uanChannel = CreateObject<UanChannel>();
    Ptr<UanPropModelIdeal> uanProp = CreateObject<UanPropModelIdeal>();
    Ptr<UanNoiseModelDefault> uanNoise = CreateObject<UanNoiseModelDefault>();
    uanChannel->SetPropagationModel(uanProp);
    uanChannel->SetNoiseModel(uanNoise);

    // 配置UAN PHY和MAC
    uan.SetMac("ns3::UanMacAloha");
    uan.SetPhy("ns3::UanPhyGen");

    // 安装UAN设备到水下节点
    NetDeviceContainer uanDevices = uan.Install(uanNodes, uanChannel);

    // 安装UAN设备到浮标节点
    NetDeviceContainer buoyUanDevice = uan.Install(buoyNode, uanChannel);

    NS_LOG_INFO("UAN devices installed");

    // 创建WiFi信道和设备
    YansWifiChannelHelper wifiChannel = YansWifiChannelHelper::Default();
    YansWifiPhyHelper wifiPhy;
    wifiPhy.SetChannel(wifiChannel.Create());

    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211n);
    wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                 "DataMode", StringValue("HtMcs7"),
                                 "ControlMode", StringValue("HtMcs0"));

    WifiMacHelper wifiMac;
    Ssid ssid = Ssid("underwater-surface-network");

    // 浮标作为AP
    wifiMac.SetType("ns3::ApWifiMac", "Ssid", SsidValue(ssid));
    NetDeviceContainer buoyWifiDevice = wifi.Install(wifiPhy, wifiMac, buoyNode);

    // 水面节点作为STA
    wifiMac.SetType("ns3::StaWifiMac",
                    "Ssid", SsidValue(ssid),
                    "ActiveProbing", BooleanValue(false));
    NetDeviceContainer surfaceWifiDevices = wifi.Install(wifiPhy, wifiMac, surfaceNodes);

    NS_LOG_INFO("WiFi devices installed");

    // 安装协议栈
    InternetStackHelper stack;
    stack.Install(uanNodes);
    stack.Install(buoyNode);
    stack.Install(surfaceNodes);

    // 分配IP地址
    Ipv4AddressHelper address;

    // UAN网络地址
    address.SetBase("********", "*************");
    Ipv4InterfaceContainer uanInterfaces = address.Assign(uanDevices);
    Ipv4InterfaceContainer buoyUanInterface = address.Assign(buoyUanDevice);

    // WiFi网络地址
    address.SetBase("***********", "*************");
    Ipv4InterfaceContainer buoyWifiInterface = address.Assign(buoyWifiDevice);
    Ipv4InterfaceContainer surfaceWifiInterfaces = address.Assign(surfaceWifiDevices);

    NS_LOG_INFO("IP addresses assigned");

    // 设置路由
    Ipv4GlobalRoutingHelper::PopulateRoutingTables();

    // 创建并安装中继应用到浮标节点
    Ptr<RelayApplication> relayApp = CreateObject<RelayApplication>();
    relayApp->SetUanAddress(buoyUanInterface.GetAddress(0));
    relayApp->SetWifiAddress(buoyWifiInterface.GetAddress(0));
    buoyNode.Get(0)->AddApplication(relayApp);
    relayApp->SetStartTime(Seconds(0.0));
    relayApp->SetStopTime(Seconds(100.0));

    NS_LOG_INFO("Relay application installed on buoy");

    // 创建简单的测试应用程序
    uint16_t relayPort = 9999;

    // UAN节点发送UDP数据到中继
    UdpClientHelper uanClient(buoyUanInterface.GetAddress(0), relayPort);
    uanClient.SetAttribute("MaxPackets", UintegerValue(5));
    uanClient.SetAttribute("Interval", TimeValue(Seconds(2.0)));
    uanClient.SetAttribute("PacketSize", UintegerValue(1024));

    ApplicationContainer uanClientApps = uanClient.Install(uanNodes.Get(0));
    uanClientApps.Start(Seconds(5.0));
    uanClientApps.Stop(Seconds(15.0));

    // 水面节点发送UDP数据到中继
    UdpClientHelper wifiClient(buoyWifiInterface.GetAddress(0), relayPort);
    wifiClient.SetAttribute("MaxPackets", UintegerValue(3));
    wifiClient.SetAttribute("Interval", TimeValue(Seconds(3.0)));
    wifiClient.SetAttribute("PacketSize", UintegerValue(512));

    ApplicationContainer wifiClientApps = wifiClient.Install(surfaceNodes.Get(0));
    wifiClientApps.Start(Seconds(20.0));
    wifiClientApps.Stop(Seconds(30.0));

    // 在所有节点上安装UDP服务器来接收转发的数据
    UdpServerHelper serverHelper(relayPort);
    ApplicationContainer serverApps = serverHelper.Install(uanNodes);
    serverApps.Add(serverHelper.Install(surfaceNodes));
    serverApps.Start(Seconds(1.0));
    serverApps.Stop(Seconds(100.0));

    NS_LOG_INFO("Applications installed");

    // 配置跟踪
    // UAN PHY跟踪
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::UanNetDevice/Phy/PhyTxBegin",
                    MakeCallback(&UanPhyTxBeginTrace));
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::UanNetDevice/Phy/RxOk",
                    MakeCallback(&UanPhyRxTrace));

    // WiFi PHY跟踪
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::WifiNetDevice/Phy/PhyTxBegin",
                    MakeCallback(&WifiPhyTxTrace));
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::WifiNetDevice/Phy/PhyRxBegin",
                    MakeCallback(&WifiPhyRxTrace));

    // 应用层跟踪 - UdpServer
    Config::Connect("/NodeList/*/ApplicationList/*/$ns3::UdpServer/Rx",
                    MakeCallback(&PacketReceived));

    NS_LOG_INFO("Tracing configured");

    // 启用PCAP跟踪
    wifiPhy.EnablePcapAll("underwater-surface-wifi");

    // 运行仿真
    NS_LOG_INFO("Starting simulation...");
    Simulator::Stop(Seconds(100.0));
    Simulator::Run();

    // 输出统计信息
    std::cout << "\n=== 仿真统计结果 ===" << std::endl;
    std::cout << "UAN发送数据包总数: " << g_uanTxCount << std::endl;
    std::cout << "UAN接收数据包总数: " << g_uanRxCount << std::endl;
    std::cout << "WiFi发送数据包总数: " << g_wifiTxCount << std::endl;
    std::cout << "WiFi接收数据包总数: " << g_wifiRxCount << std::endl;
    std::cout << "浮标从UAN接收数据包: " << g_buoyUanRxCount << std::endl;
    std::cout << "浮标从WiFi接收数据包: " << g_buoyWifiRxCount << std::endl;
    std::cout << "浮标转发到UAN数据包: " << g_buoyUanTxCount << std::endl;
    std::cout << "浮标转发到WiFi数据包: " << g_buoyWifiTxCount << std::endl;
    std::cout << "===================" << std::endl;

    // 验证通信是否正常
    bool communicationSuccess = (g_uanTxCount > 0 && g_uanRxCount > 0 &&
                                g_wifiTxCount > 0 && g_wifiRxCount > 0 &&
                                g_buoyUanRxCount > 0 && g_buoyWifiRxCount > 0);

    if (communicationSuccess)
    {
        std::cout << "\n✓ 通信测试成功：所有节点都有数据包收发记录" << std::endl;
    }
    else
    {
        std::cout << "\n✗ 通信测试失败：存在节点没有数据包收发记录" << std::endl;
    }

    Simulator::Destroy();
    return 0;
}
