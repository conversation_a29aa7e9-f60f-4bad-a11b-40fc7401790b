# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# single_source_file_scratches at scratch/CMakeLists.txt:67 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/[^.]*.cc")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/scratch-simulator.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/underwater-surface-relay.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()

# scratch_sources at scratch/CMakeLists.txt:98 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/lib/[^.]*.cc")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/lib/scratch-nested-subdir-library-source.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()

# scratch_sources at scratch/CMakeLists.txt:98 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/[^.]*.cc")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/scratch-subdir-additional-header.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/scratch-subdir.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()

# scratch_subdirectories at scratch/CMakeLists.txt:74 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/**")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/CMakeLists.txt"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/README.md"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/CMakeLists.txt"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/lib"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/lib/scratch-nested-subdir-library-header.h"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/lib/scratch-nested-subdir-library-source.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/nested-subdir/scratch-nested-subdir-executable.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/scratch-simulator.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/scratch-subdir-additional-header.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/scratch-subdir-additional-header.h"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/subdir/scratch-subdir.cc"
  "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/scratch/underwater-surface-relay.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()
