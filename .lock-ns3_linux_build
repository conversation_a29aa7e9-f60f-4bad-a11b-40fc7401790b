#! /usr/bin/env python3

launch_dir = '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40'
run_dir = '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40'
top_dir = '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40'
out_dir = '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build'


NS3_ENABLED_MODULES = ['ns3-wimax', 'ns3-wifi', 'ns3-virtual-net-device', 'ns3-uan', 'ns3-traffic-control', 'ns3-topology-read', 'ns3-tap-bridge', 'ns3-stats', 'ns3-spectrum', 'ns3-sixlowpan', 'ns3-propagation', 'ns3-point-to-point-layout', 'ns3-point-to-point', 'ns3-olsr', 'ns3-nix-vector-routing', 'ns3-network', 'ns3-netanim', 'ns3-mobility', 'ns3-mesh', 'ns3-lte', 'ns3-lr-wpan', 'ns3-internet-apps', 'ns3-internet', 'ns3-flow-monitor', 'ns3-fd-net-device', 'ns3-energy', 'ns3-dsr', 'ns3-dsdv', 'ns3-csma-layout', 'ns3-csma', 'ns3-core', 'ns3-config-store', 'ns3-buildings', 'ns3-bridge', 'ns3-aqua-sim-ng', 'ns3-applications', 'ns3-aodv', 'ns3-antenna', ]
NS3_ENABLED_CONTRIBUTED_MODULES = []
NS3_MODULE_PATH = ['/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/remote-cli', '/usr/local/sbin', '/usr/local/bin', '/usr/sbin', '/usr/bin', '/sbin', '/bin', '/usr/games', '/usr/local/games', '/snap/bin', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/lib']
ENABLE_REAL_TIME = False
ENABLE_EXAMPLES = True
ENABLE_TESTS = True
ENABLE_OPENFLOW = False
NSCLICK = False
ENABLE_BRITE = False
ENABLE_SUDO = False
ENABLE_PYTHON_BINDINGS = False
EXAMPLE_DIRECTORIES = ['wireless', 'udp-client-server', 'udp', 'tutorial', 'traffic-control', 'tcp', 'stats', 'socket', 'routing', 'realtime', 'naming', 'matrix-topology', 'ipv6', 'error-model', 'energy', 'channel-models', ]
APPNAME = 'ns'
BUILD_PROFILE = 'debug'
VERSION = '3.40' 
BUILD_VERSION_STRING = '' 
PYTHON = ['/usr/bin/python3']
VALGRIND_FOUND = True 


ns3_runnable_programs = ['/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/utils/perf/ns3.40-perf-io-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/utils/ns3.40-print-introspected-doxygen-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/utils/ns3.40-bench-packets-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/utils/ns3.40-bench-scheduler-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/utils/ns3.40-test-runner-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/subdir/ns3.40-scratch-subdir-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/nested-subdir/ns3.40-scratch-nested-subdir-executable-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/ns3.40-underwater-relay-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/scratch/ns3.40-scratch-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-eht-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ofdm-eht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-wired-bridging-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-vht-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-txop-aggregation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-timing-attributes-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-tcp-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-spectrum-saturation-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-spectrum-per-interference-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-spectrum-per-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-spatial-reuse-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-sleep-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-simple-interference-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-simple-infra-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-simple-ht-hidden-stations-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-simple-adhoc-grid-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-simple-adhoc-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-rate-adaptation-distance-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-power-adaptation-interference-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-power-adaptation-distance-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-error-models-comparison-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ofdm-vht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ofdm-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ofdm-ht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ofdm-he-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-multirate-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-multi-tos-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-mixed-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ht-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-hidden-terminal-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-he-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-dsss-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-clear-channel-cmu-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-blockack-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-backward-compatibility-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-ap-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-aggregation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-adhoc-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-80211n-mimo-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-wifi-80211e-txop-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/wireless/ns3.40-mixed-wired-wireless-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/udp-client-server/ns3.40-udp-trace-client-server-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/udp-client-server/ns3.40-udp-client-server-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/udp/ns3.40-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-seventh-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-sixth-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-fifth-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-fourth-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-third-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-second-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-first-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tutorial/ns3.40-hello-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-cobalt-vs-codel-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-tbf-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-red-vs-nlred-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-red-vs-fengadaptive-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-queue-discs-benchmark-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/traffic-control/ns3.40-traffic-control-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-dctcp-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-validation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-linux-reno-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-pacing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-variants-comparison-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-pcap-nanosec-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-bulk-send-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-bbr-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-star-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-star-server-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/tcp/ns3.40-tcp-large-transfer-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/stats/ns3.40-wifi-example-sim-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/socket/ns3.40-socket-options-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/socket/ns3.40-socket-options-ipv4-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/socket/ns3.40-socket-bound-tcp-static-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/socket/ns3.40-socket-bound-static-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-simple-multicast-flooding-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-global-routing-multi-switch-plus-router-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-rip-simple-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-ripng-simple-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-manet-routing-compare-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-simple-routing-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-mixed-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-simple-alternate-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-simple-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-global-injection-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-global-routing-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-static-routing-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/routing/ns3.40-dynamic-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/realtime/ns3.40-realtime-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/naming/ns3.40-object-names-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/matrix-topology/ns3.40-matrix-topology-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-fragmentation-ipv6-PMTU-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-wsn-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-test-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-radvd-two-prefix-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-radvd-one-prefix-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-ping6-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-loose-routing-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-icmpv6-redirect-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-fragmentation-ipv6-two-MTU-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/ipv6/ns3.40-fragmentation-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/error-model/ns3.40-simple-error-model-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/energy/ns3.40-energy-model-with-harvesting-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/energy/ns3.40-energy-model-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples/channel-models/ns3.40-three-gpp-v2v-channel-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wimax/examples/ns3.40-wimax-simple-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wimax/examples/ns3.40-wimax-multicast-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wimax/examples/ns3.40-wimax-ipv4-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-bianchi-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-phy-configuration-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-trans-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-manager-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-test-interference-helper-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/wifi/examples/ns3.40-wifi-phy-test-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/virtual-net-device/examples/ns3.40-virtual-net-device-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-6lowpan-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-raw-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-ipv6-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-ipv4-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-rc-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/uan/examples/ns3.40-uan-cw-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-fqcodel-l4s-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-pie-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-codel-vs-pfifo-asymmetric-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-codel-vs-pfifo-basic-test-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-pfifo-vs-red-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-adaptive-red-tests-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-red-vs-ared-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/traffic-control/examples/ns3.40-red-tests-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/topology-read/examples/ns3.40-topology-example-sim-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/tap-bridge/ns3.40-tap-creator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/tap-bridge/examples/ns3.40-tap-wifi-dumbbell-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/tap-bridge/examples/ns3.40-tap-wifi-virtual-machine-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/tap-bridge/examples/ns3.40-tap-csma-virtual-machine-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/tap-bridge/examples/ns3.40-tap-csma-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-file-helper-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-file-aggregator-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-gnuplot-helper-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-gnuplot-aggregator-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-double-probe-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-gnuplot-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/stats/examples/ns3.40-time-probe-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-three-gpp-two-ray-channel-calibration-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-three-gpp-channel-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-tv-trans-regional-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-tv-trans-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-adhoc-aloha-ideal-phy-with-microwave-oven-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-adhoc-aloha-ideal-phy-matrix-propagation-loss-model-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/spectrum/examples/ns3.40-adhoc-aloha-ideal-phy-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/sixlowpan/examples/ns3.40-example-ping-lr-wpan-mesh-under-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/sixlowpan/examples/ns3.40-example-ping-lr-wpan-beacon-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/sixlowpan/examples/ns3.40-example-ping-lr-wpan-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/sixlowpan/examples/ns3.40-example-sixlowpan-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/propagation/examples/ns3.40-jakes-propagation-model-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/propagation/examples/ns3.40-main-propagation-loss-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/point-to-point/examples/ns3.40-main-attribute-value-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/olsr/examples/ns3.40-simple-point-to-point-olsr-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/olsr/examples/ns3.40-olsr-hna-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/nix-vector-routing/examples/ns3.40-nix-double-wifi-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/nix-vector-routing/examples/ns3.40-nms-p2p-nix-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/nix-vector-routing/examples/ns3.40-nix-simple-multi-address-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/nix-vector-routing/examples/ns3.40-nix-simple-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/network/examples/ns3.40-lollipop-comparisons-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/network/examples/ns3.40-packet-socket-apps-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/network/examples/ns3.40-main-packet-tag-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/network/examples/ns3.40-main-packet-header-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/network/examples/ns3.40-bit-serializer-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-uan-animation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-wireless-animation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-resources-counters-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-colors-link-description-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-star-animation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-grid-animation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/netanim/examples/ns3.40-dumbbell-animation-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-reference-point-group-mobility-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-mobility-trace-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-main-grid-topology-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-ns2-mobility-trace-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-main-random-walk-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-main-random-topology-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mobility/examples/ns3.40-bonnmotion-ns2-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/mesh/examples/ns3.40-mesh-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-simple-epc-emu-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-x2-handover-measures-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-x2-handover-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-uplink-power-control-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-simple-epc-backhaul-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-simple-epc-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-simple-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-rlc-traces-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-rem-sector-antenna-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-rem-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-radio-link-failure-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-profiling-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-pathloss-traces-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-ipv6-ue-ue-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-ipv6-ue-rh-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-ipv6-addr-conf-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-intercell-interference-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-frequency-reuse-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-fading-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-dual-stripe-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-distributed-ffr-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-deactivate-bearer-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-cqi-threshold-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lte/examples/ns3.40-lena-cc-helper-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-per-plot-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-bootstrap-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-error-model-plot-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-error-distance-plot-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-orphan-scan-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-active-scan-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-ed-scan-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-phy-test-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-packet-print-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-mlme-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/lr-wpan/examples/ns3.40-lr-wpan-data-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet-apps/examples/ns3.40-ping-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet-apps/examples/ns3.40-traceroute-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet-apps/examples/ns3.40-dhcp-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet/examples/ns3.40-neighbor-cache-dynamic-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet/examples/ns3.40-neighbor-cache-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/internet/examples/ns3.40-main-simple-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-tap-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-tap-ping-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-emu-tc-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-emu-send-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-emu-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-emu-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd-emu-ping-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-realtime-fd2fd-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-realtime-dummy-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-fd2fd-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/examples/ns3.40-dummy-network-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/ns3.40-tap-device-creator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/fd-net-device/ns3.40-raw-sock-creator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/energy/examples/ns3.40-basic-energy-model-test-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/energy/examples/ns3.40-rv-battery-model-test-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/energy/examples/ns3.40-li-ion-energy-source-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/examples//ns3.40-generic-battery-wifiradio-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/energy/examples/ns3.40-generic-battery-discharge-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/dsr/examples/ns3.40-dsr-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/dsdv/examples/ns3.40-dsdv-manet-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma-layout/examples/ns3.40-csma-star-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-ping-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-raw-ip-socket-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-multicast-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-packet-socket-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-broadcast-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/csma/examples/ns3.40-csma-one-subnet-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-log-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-empirical-random-variable-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-main-test-sync-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-main-random-variable-stream-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-test-string-value-formatting-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-system-path-examples-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-sample-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-sample-show-progress-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-sample-random-variable-stream-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-sample-random-variable-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-sample-log-time-format-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-main-ptr-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-main-callback-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-length-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-hash-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-fatal-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-command-line-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/core/examples/ns3.40-assert-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/config-store/examples/ns3.40-config-store-save-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/buildings/examples/ns3.40-outdoor-random-walk-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/buildings/examples/ns3.40-outdoor-group-mobility-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/buildings/examples/ns3.40-buildings-pathloss-profiler-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/bridge/examples/ns3.40-csma-bridge-one-hop-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/bridge/examples/ns3.40-csma-bridge-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-JmacTest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-TrumacTest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-SfamaGridTest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-AlohaGridTest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-LibraGridTest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-FloodingMac-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-VBF-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-ND_example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-bMAC-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-ddos-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-GOAL_string-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-floodtest-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aqua-sim-ng/ns3.40-broadcastMAC_example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/applications/examples/ns3.40-three-gpp-http-example-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/build/src/aodv/examples/ns3.40-aodv-debug', '/home/<USER>/ns3/ns-allinone-3.40/ns-3.40/cmake-cache/ns3.40-stdlib_pch_exec-debug', ]

ns3_runnable_scripts = []

